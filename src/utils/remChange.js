// 初始化字体大小rem
var fontSize = 0;
(function(doc,win){
  //获取html节点和事件 和屏幕变化改变字体大小
  const docEl = doc.documentElement,
    resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',
    recalc = function(){
        const clientWidth = docEl.clientWidth;
        if(!clientWidth) return;//排除宽度没值
        if(clientWidth >= 750){
          docEl.style.fontSize = '100px';
        }else{
          docEl.style.fontSize = 100 * (clientWidth / 750) + 'px';
        }
    };
    function fontSizeFun(){
        var size = 0;
        var clientWidth = docEl.clientWidth;
        if(!clientWidth) return;//排除宽度没值
        if(clientWidth >= 750){
         size = 100;
        }else{
          size = 100 * (clientWidth / 750);
        }
        return size;
    }
    if (!doc.addEventListener) return;
    win.addEventListener(resizeEvt,recalc,false);
    doc.addEventListener("DOMContentLoaded",recalc,false);
    fontSize = fontSizeFun();
    win.addEventListener(resizeEvt,function(){
        fontSize=fontSizeFun();
    },false);
    doc.addEventListener("DOMContentLoaded",function(){
        fontSize=fontSizeFun();
    },false);
})(document,window);