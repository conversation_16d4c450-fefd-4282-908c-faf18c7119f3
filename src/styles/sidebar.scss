#app {
  // 主体区域
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: 180px;
  } // 侧边栏
  .sidebar-container {
    transition: width 0.28s;
    width: 180px!important;
    height: 100%;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    a {
      display: inline-block;
      width: 100%;
    }
    .svg-icon {
      margin-right: 16px;
    }
    .el-menu {
      border: none;
      width: 100%;
    }
  }
  .hideSidebar {
    .sidebar-container,.sidebar-container .el-menu {
      width: 36px!important;
      // overflow: inherit;
    }
    .main-container {
      margin-left: 36px;
    }
  }
  .hideSidebar {
    .submenu-title-noDropdown {
      padding-left: 10px!important;
      position: relative;
      span {
        height: 0;
        width: 0;
        overflow: hidden;
        visibility: hidden;
        transition: opacity .3s cubic-bezier(.55, 0, .1, 1);
        opacity: 0;
        display: inline-block;
      }
      &:hover {
        span {
          display: block;
          border-radius: 3px;
          z-index: 1002;
          width: 140px;
          height: 56px;
          visibility: visible;
          position: absolute;
          right: -145px;
          text-align: left;
          text-indent: 20px;
          top: 0px;
          background-color: $subMenuBg!important;
          opacity: 1;
        }
      }
    }
    .el-submenu {
      &>.el-submenu__title {
        padding-left: 10px!important;
        &>span {
          display: none;
        }
        .el-submenu__icon-arrow {
          display: none;
        }
      }
      .nest-menu {
        .el-submenu__icon-arrow {
          display: block!important;
        }
        span {
          display: inline-block!important;
        }
      }
    }
  }
  .nest-menu .el-submenu>.el-submenu__title,
  .el-submenu .el-menu-item {
    min-width: 180px!important;
    background-color: $subMenuBg!important;
    &:hover {
      background-color: $menuHover!important;
    }
  }
  .el-menu--collapse .el-menu .el-submenu{
    min-width: 180px!important;
  }
}
