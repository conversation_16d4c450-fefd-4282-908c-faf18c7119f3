<template
  ><el-form
    class="small-space"
    :model="tempUser"
    label-position="left"
    label-width="80px"
    style="width: 300px; margin-left:50px;"
  >
    <el-form-item label="用户名" required v-if="dialogStatus == 'create'">
      <el-input type="text" v-model="tempUser.username"> </el-input>
    </el-form-item>
    <el-form-item label="密码" v-if="dialogStatus == 'create'" required>
      <el-input type="password" v-model="tempUser.password"> </el-input>
    </el-form-item>
    <el-form-item label="新密码" v-else>
      <el-input
        type="password"
        v-model="tempUser.password"
        placeholder="不填则表示不修改"
      >
      </el-input>
    </el-form-item>
    <el-form-item label="角色" required>
      <el-select v-model="tempUser.roleId" placeholder="请选择">
        <el-option
          v-for="item in roles"
          :key="item.roleId"
          :label="item.roleName"
          :value="item.roleId"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="昵称" required>
      <el-input type="text" v-model="tempUser.nickname"> </el-input>
    </el-form-item>
  </el-form>
</template>
