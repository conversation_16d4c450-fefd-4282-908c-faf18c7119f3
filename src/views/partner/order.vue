<template>
    <div class="app-container">
        <el-table :data="list" v-loading.body="listLoading" element-loading-text="拼命加载中" border fit
            highlight-current-row>
            <el-table-column align="center" label="序号" width="80">
                <template slot-scope="scope">
                    <span v-text="getIndex(scope.$index)"> </span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="content" label="金额(元)" width="170">
                <template slot-scope="scope">
                    <span>{{ (scope.row.amount / 100).toFixed(2) }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="付款时间" width="170">
                <template slot-scope="scope">
                    <span>{{ scope.row.create_time }}</span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-size="listQuery.pageSize" layout="prev, pager, next, jumper" :total="totalCount">
        </el-pagination>

    </div>
</template>
<script>
export default {
    data() {
        return {
            searchtext: "",
            totalCount: 0, //分页组件--数据总条数
            list: [], //表格的数据
            listLoading: false, //数据加载等待动画
            listQuery: {
                pageNum: 1, //页码
                pageSize: 40, //每页条数

            },
            istrie: false,
            listQuerywordSimp: ""
        };
    },
    created() {
        this.getList();
    },
    methods: {
        getList() {
            //查询列表
            if (this.hasPerm("pay:list") || this.hasPerm("pay:show")) {
                this.listLoading = true;
                this.api({
                    url: "/pay/listAll",
                    method: "get",
                    params: this.listQuery
                }).then(data => {
                    this.listLoading = false;
                    this.list = data.list;
                    this.totalCount = data.totalCount;
                });
            }
        },
        handleSizeChange(val) {
            //改变每页数量
            this.listQuery.pageRow = val;
            // this.handleFilter();
        },
        handleCurrentChange(val) {
            //改变页码
            this.listQuery.pageNum = val;
            this.getList();
        },
        getIndex($index) {
            //表格序号
            // console.log($index)
            return (this.listQuery.pageNum - 1) * this.listQuery.pageSize + $index + 1;
        },
    }
};
</script>
<style scoped>
.filter-container {
    width: 500px;
    display: flex;
    flex-direction: row;
    justify-content: start
}

.musadlkl {
    margin-left: 20px;
    display: flex;
    flex-direction: row;
    justify-content: start
}

.musadlkl div {
    margin-right: 20px;
}
</style>