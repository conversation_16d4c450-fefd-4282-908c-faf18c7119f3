<template>
    <div class="app-container">
        <div class="filter-container">
            <div>
                <el-form>
                    <el-form-item>
                        <el-button type="primary" icon="plus" @click="showCreate" v-if="hasPerm('article:add')">添加
                        </el-button>

                    </el-form-item>

                </el-form>
            </div>
            <!-- <div class="musadlkl">
                <div>
                    <el-input placeholder="请输入内容" type="text" v-model="listQuerywordSimp">
                    </el-input>
                </div>
                <div>
                    <el-button type="primary" icon="plus" @click="search">搜索
                    </el-button>
                </div>


            </div> -->


        </div>
        <el-table :data="list" v-loading.body="listLoading" element-loading-text="拼命加载中" border fit
            highlight-current-row>
            <el-table-column align="center" label="序号" width="80">
                <template slot-scope="scope">
                    <span v-text="getIndex(scope.$index)"> </span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="content" label="名称" width="170">
                <template slot-scope="scope">
                    <span>{{ scope.row.name }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="联系人" width="170">
                <template slot-scope="scope">
                    <span>{{ scope.row.person }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="content" label="电话" width="170">
                <template slot-scope="scope">
                    <span>{{ scope.row.phone }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" prop="content" label="地址" width="170">
                <template slot-scope="scope">
                    <span>{{ scope.row.address }}</span>
                </template>
            </el-table-column>
            <el-table-column align="center" label="管理" width="350"
                v-if="hasPerm('article:update') || hasPerm('article:delete')">
                <template slot-scope="scope">
                    <el-button v-if="hasPerm('article:update')" type="primary" icon="edit"
                        @click="showUpdate(scope.$index)">修改</el-button>
                    <el-button v-if="hasPerm('article:delete')" type="danger" icon="edit"
                        @click="deletedate(scope.row.id)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--    -->
        <!-- <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
     :current-page="listQuery.pageNum"  
      :page-size="listQuery.pageSize"
      :total="totalCount"
      :page-sizes="[10, 20, 50, 100]" 
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination> -->
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-size="listQuery.pageSize" layout="prev, pager, next, jumper" :total="totalCount">
        </el-pagination>


        <!--  :current-page.sync="listQuery.pageNum"
      :page-size="listQuery.pageSize" -->



        <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
            <el-form class="small-space" :model="tempArticle" label-position="left" label-width="60px"
                style='width: 300px; margin-left:50px;'>
                <el-form-item label="名称">
                    <el-input type="text" :disabled="istrie" v-model="tempArticle.name">
                    </el-input>
                </el-form-item>
                <el-form-item label="联系人">
                    <el-input type="text" v-model="tempArticle.person">
                    </el-input>
                </el-form-item>
                <el-form-item label="电话">
                    <el-input type="text" v-model="tempArticle.phone">
                    </el-input>
                </el-form-item>
                <el-form-item label="地址">
                    <el-input type="text" v-model="tempArticle.address">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogFormVisible = false">取 消</el-button>
                <el-button v-if="dialogStatus == 'create'" type="success" @click="createArticle">创 建</el-button>
                <el-button type="primary" v-else @click="updateArticle">修 改</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
export default {
    data() {
        return {
            searchtext: "",
            totalCount: 0, //分页组件--数据总条数
            list: [], //表格的数据
            listLoading: false, //数据加载等待动画
            listQuery: {
                pageNum: 1, //页码
                pageSize: 40, //每页条数

            },
            istrie: false,
            listQuerywordSimp: "",
            dialogStatus: "create",
            dialogFormVisible: false,
            textMap: {
                update: "编辑",
                create: "创建"
            },
            tempArticle: {
                name: "",
                person: "",
                phone: "",
                address: "",
                type: "",
            }
        };
    },
    created() {
        this.getList();
    },
    methods: {
        search() {
            this.listQuery.name = this.listQuerywordSimp

            this.getList()
        },
        getList() {
            //查询列表
            if (!this.hasPerm("partner:list")) {
                return;
            }
            this.listLoading = true;
            this.api({
                url: "/partner/listPartner",
                method: "get",
                params: this.listQuery
            }).then(data => {
                // console.log(res)
                this.listLoading = false;
                this.list = data.list;
                this.totalCount = data.totalCount;
            });
        },
        handleSizeChange(val) {
            //改变每页数量
            this.listQuery.pageRow = val;
            // this.handleFilter();
        },
        handleCurrentChange(val) {
            //改变页码
            this.listQuery.pageNum = val;
            this.getList();
        },
        getIndex($index) {
            //表格序号
            // console.log($index)
            return (this.listQuery.pageNum - 1) * this.listQuery.pageSize + $index + 1;
        },
        showCreate() {
            // 显示新增对话框

            this.dialogStatus = "create";
            this.istrie = false
            this.tempArticle.name = "";
            this.tempArticle.person = "";
            // this.tempArticle.id="";
            this.tempArticle.phone = "";
            this.tempArticle.address = "";
            this.tempArticle.type = "ADD"
            this.dialogFormVisible = true;
        },
        showUpdate($index) {
            //TODO 显示修改对话框
            console.log($index)
            this.istrie = true
            this.tempArticle.id = this.list[$index].id;
            this.tempArticle.name = this.list[$index].name;
            this.tempArticle.person = this.list[$index].person;
            this.tempArticle.phone = this.list[$index].phone;
            this.tempArticle.address = this.list[$index].address;
            this.tempArticle.type = "EDIT"
            this.dialogStatus = "update";
            this.dialogFormVisible = true;
        },
        createArticle() {

            if (!this.tempArticle.name) {
                this.$message({
                    type: 'success',
                    message: '名称不能为空!'
                });
                return false;
            } else if (!this.tempArticle.person) {
                this.$message({
                    type: 'success',
                    message: '联系人不能为空!'
                });
                return false;
            } else if (!this.tempArticle.phone) {
                this.$message({
                    type: 'success',
                    message: '电话不能为空!'
                });
                return false;
            } else if (!this.tempArticle.address) {
                this.$message({
                    type: 'success',
                    message: '地址不能为空!'
                });
                return false;
            }


            //保存新文章
            this.api({
                url: "/partner/addPartner",
                method: "get",
                params: this.tempArticle
            }).then(() => {
                this.getList();
                this.dialogFormVisible = false;
            });
        },
        deletedate(text) {

            this.$confirm('此操作将永久删除该文字, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
                this.tempArticle = {}
                this.tempArticle.id = text;
                this.tempArticle.type = "DEL"
                this.api({
                    url: "/partner/deletePartner",
                    method: "get",
                    params: this.tempArticle
                }).then(() => {
                    this.listQuerywordSimp = "";
                    this.listQuery.name = "";
                    console.log()
                    this.getList();
                    // this.dialogFormVisible = false;
                });

            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });











        },
        updateArticle() {
            if (!this.tempArticle.person) {
                this.$message({
                    type: 'success',
                    message: '联系人不能为空!'
                });
                return false;
            } else if (!this.tempArticle.phone) {
                this.$message({
                    type: 'success',
                    message: '电话不能为空!'
                });
                return false;
            } else if (!this.tempArticle.address) {
                this.$message({
                    type: 'success',
                    message: '地址不能为空!'
                });
                return false;
            }


            //修改文章
            this.api({
                url: "/partner/updatePartner",
                method: "get",
                params: this.tempArticle
            }).then(() => {
                this.getList();
                this.dialogFormVisible = false;
            });
        }
    }
};
</script>
<style scoped>
.filter-container {
    width: 500px;
    display: flex;
    flex-direction: row;
    justify-content: start
}

.musadlkl {
    margin-left: 20px;
    display: flex;
    flex-direction: row;
    justify-content: start
}

.musadlkl div {
    margin-right: 20px;
}
</style>