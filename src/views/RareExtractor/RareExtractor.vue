<template>
  <div class="app-container">
    <div class="tablelist">
    <el-table
      :data="list"
     
      v-loading.body="listLoading"
      element-loading-text="拼命加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column
        align="center"
        label="序号"
        width="80"
      >
        <template slot-scope="scope">
          <span v-text="getIndex(scope.$index)"> </span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="content"
        label="生僻字"
        width="170"
      >
        <template slot-scope="scope">
          <span>{{scope.row.word}}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="创建时间"
        width="170"
      >
        <template slot-scope="scope">
          <span>{{scope.row.createTime}}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="content"
        label="更新时间"
        width="170"
      >
       <template slot-scope="scope">
          <span>{{scope.row.updateTime}}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="管理"
        width="350"
        v-if="hasPerm('RareExtractor:update')"
      >
        <template slot-scope="scope">
          <el-button
          v-if="hasPerm('RareExtractor:update')"
            type="primary"
            icon="edit"
            @click="showUpdate(scope.$index)"
          >添加到字库</el-button>
          
        </template>
      </el-table-column>
    </el-table>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="listQuery.pageNum"
      :page-size="listQuery.pageRow"
      :total="totalCount"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
    >
    </el-pagination>
    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
    >
      <el-form
        class="small-space"
        :model="tempArticle"
        label-position="left"
        label-width="60px"
        style='width: 300px; margin-left:50px;'
      >
        <el-form-item label="简体">
          <el-input
            type="text"
            v-model="tempArticle.errorWordSimp"
          >
          </el-input>
          
        </el-form-item>
        <el-form-item label="繁体">
          <el-input
            type="text"
            v-model="tempArticle.errorWordTrad"
          >
          </el-input>
          
        </el-form-item>
         <el-form-item label="笔画">
          <el-input
            type="text"
            v-model="tempArticle.stroke"
          >
          </el-input>
          
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button
          v-if="dialogStatus=='create'"
          type="success"
          @click="createArticle"
        >创 建</el-button>
        <el-button
          type="primary"
          v-else
          @click="updateArticle"
        >添 加</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      searchtext:"",
      totalCount: 0, //分页组件--数据总条数
      list: [], //表格的数据
      listLoading: false, //数据加载等待动画
      listQuery: {
        pageNum: 1, //页码
        pageSize: 40, //每页条数
        wordSimp:""
      },
      dialogStatus: "create",
      dialogFormVisible: false,
      textMap: {
        update: "编辑",
        create: "创建文字"
      },
      tempArticle: {
        errorWordSimp:"",
        errorWordTrad:"",
        stroke:"",
     
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    search(){
        this.getList()
    },
    getList() {
      //查询列表
      if (!this.hasPerm("article:list")) {
        return;
      }
      this.listLoading = true;
      this.api({
        url: "/predictionManager/getAllNewErrorWord",
        method: "get",
        params: this.listQuery
      }).then(data => {
        // console.log(res)
        this.listLoading = false;
        this.list = data.list;
        this.totalCount = data.totalCount;
      });
    },
    handleSizeChange(val) {
      //改变每页数量
      this.listQuery.pageRow = val;
      this.handleFilter();
    },
    handleCurrentChange(val) {
      //改变页码
      this.listQuery.pageNum = val;
      this.getList();
    },
    getIndex($index) {
      //表格序号
      // console.log($index)
      return (this.listQuery.pageNum - 1) * this.listQuery.pageSize + $index + 1;
    },
    showCreate() {
      //显示新增对话框
     
      this.dialogStatus = "create";
        this.tempArticle.wordSimp = "";
       this.tempArticle.wordTrad = "";
        this.tempArticle.stroke = "";
          this.tempArticle.type ="ADD"
      this.dialogFormVisible = true;
    },
    showUpdate($index) {
      //显示修改对话框
      console.log($index)
    
      this.tempArticle.errorId = this.list[$index].id;
       this.tempArticle.errorWordSimp=this.list[$index].word;
      
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
    },
    createArticle() {
      //保存新文章
      this.api({
        url: "/predictionManager/operateWord",
        method: "get",
        params: this.tempArticle
      }).then(() => {
        this.getList();
        this.dialogFormVisible = false;
      });
    },
    deletedate(text){
      this.tempArticle={}
      this.tempArticle.wordSimp=text;
       this.tempArticle.type ="DEL"
          this.api({
        url: "/predictionManager/operateWord",
        method: "get",
        params:this.tempArticle
      }).then(() => {
        this.getList();
        // this.dialogFormVisible = false;
      });
    },
    updateArticle() {
      //修改文章
      this.api({
        url: "/predictionManager/delErrorWord",
        method: "post",
        params: this.tempArticle
      }).then(() => {
           this.dialogFormVisible = false;
        this.getList();
        // this.dialogFormVisible = false;
      });
    }
  }
};
</script>
<style scoped>
.filter-container{
  width: 500px;
  display: flex;
  flex-direction: row;
  justify-content: start
}
.musadlkl{
    margin-left: 20px;
     display: flex;
      flex-direction: row;
  justify-content: start
}
.musadlkl div{
  margin-right: 20px;
}
.tablelist{
    margin-bottom: 50px;
    width: 941px;
}
</style>