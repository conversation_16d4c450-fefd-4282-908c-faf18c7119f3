<template>
  <div class="content">
    <el-form
      class="small-space"
      :model="tempUser"
      label-position="left"
      label-width="80px"
      style='width: 300px; margin-left:50px;'
    >
     
      <el-form-item label="旧密码">
        <el-input
          type="password"
          v-model="tempUser.oldPwd"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="新密码">
        <el-input
          type="password"
          v-model="tempUser.newPwd"
          placeholder=""
        >
        </el-input>
      </el-form-item>

      <el-form-item required>
        <el-button
          type="primary"
          @click="updateUser"
        >修 改</el-button>
      </el-form-item>
    </el-form>
  </div>

</template>
<script>
export default {
  data() {
    return {
      tempUser: {
        name: "",
        oldPwd: "",
        newPwd: ""
      }
    };
  },
  methods: {
    updateUser() {

         if(!this.tempUser.oldPwd){
                this.$message({
            message:"请输入旧密码",
            type: 'success',
            duration: 1 * 1000,
           
          })
            return false
        }else if(!this.tempUser.newPwd){
              this.$message({
            message:"请输入新密码",
            type: 'success',
            duration: 1 * 1000,
           
          })
            return false
        }
      this.api({
        url: "/login/changePwd",
        method: "get",
        params: this.tempUser
      }).then(res => {
          console.log(res)
        if(res =="success"){
            this.$message({
            message:"修改密码成功！",
            type: 'success',
            duration: 1 * 1000,
              
          })
           this.$store.dispatch('LogOut').then(() => {
              location.reload() // 为了重新实例化vue-router对象 避免bug
               })
        }
       
      });
    }
  }
};
</script>
<style  scoped>
.content {
  padding: 50px;
}
</style>