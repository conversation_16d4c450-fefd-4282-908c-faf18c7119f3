{"name": "vue-admin", "version": "3.1.0", "license": "MIT", "description": "", "author": "", "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node build/build.js", "build:report": "npm_config_report=true node build/build.js", "lint": "eslint --ext .js,.vue src", "test": "npm run lint"}, "dependencies": {"axios": "0.17.1", "element-ui": "2.3.4", "js-cookie": "2.2.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "vue": "2.5.10", "vue-router": "3.0.1", "vuex": "3.0.1"}, "devDependencies": {"autoprefixer": "7.2.3", "babel-core": "6.26.0", "babel-eslint": "8.0.3", "babel-helper-vue-jsx-merge-props": "2.0.3", "babel-loader": "7.1.2", "babel-plugin-syntax-jsx": "6.18.0", "babel-plugin-transform-runtime": "6.23.0", "babel-plugin-transform-vue-jsx": "3.5.0", "babel-preset-env": "1.6.1", "babel-preset-stage-2": "6.24.1", "chalk": "2.3.0", "copy-webpack-plugin": "4.2.3", "css-loader": "0.28.7", "eslint": "4.13.1", "eslint-friendly-formatter": "3.0.0", "eslint-loader": "1.9.0", "eslint-plugin-html": "4.0.1", "eventsource-polyfill": "0.9.6", "extract-text-webpack-plugin": "3.0.2", "file-loader": "1.1.5", "friendly-errors-webpack-plugin": "1.6.1", "html-webpack-plugin": "2.30.1", "node-notifier": "5.1.2", "node-sass": "^4.7.2", "optimize-css-assets-webpack-plugin": "3.2.0", "ora": "1.3.0", "portfinder": "1.0.13", "postcss-import": "11.0.0", "postcss-loader": "2.0.9", "rimraf": "2.6.2", "sass-loader": "6.0.6", "semver": "5.4.1", "shelljs": "0.7.8", "svg-sprite-loader": "3.5.2", "uglifyjs-webpack-plugin": "1.1.3", "url-loader": "0.6.2", "vue-loader": "13.5.0", "vue-style-loader": "3.0.3", "vue-template-compiler": "2.5.10", "webpack": "3.10.0", "webpack-bundle-analyzer": "2.9.1", "webpack-dev-server": "2.9.7", "webpack-merge": "4.1.1"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}